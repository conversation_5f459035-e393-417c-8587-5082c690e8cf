<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 开发环境配置 - 允许HTTP流量 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 本地开发地址 -->
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>

        <!-- 常见内网IP段 -->
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">************</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********00</domain>

        <!-- 172.16.x.x 内网段 -->
        <domain includeSubdomains="true">**********</domain>
        <domain includeSubdomains="true">**********</domain>
        <domain includeSubdomains="true">***********</domain>

        <!-- 10.x.x.x 内网段 -->
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">********</domain>
        <domain includeSubdomains="true">10.1.1.1</domain>
    </domain-config>

    <!-- 全局配置 - 允许所有HTTP流量（开发环境） -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
