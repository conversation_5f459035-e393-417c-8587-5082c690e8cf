import 'package:flutter/foundation.dart';
import 'network_status_service.dart';
import 'network_error_handler.dart';
import 'http_client.dart';
import 'api_service.dart';
import 'cache_service.dart';
import 'debug_log_service.dart';
import 'localization_service.dart';

/// 应用初始化服务
class AppInitializer {
  static bool _isInitialized = false;
  
  /// 初始化所有服务
  static Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('AppInitializer: ${LocalizationService.t('debug_app_init_skip')}');
      return;
    }

    debugPrint('AppInitializer: ${LocalizationService.t('debug_app_init_start')}');
    
    try {
      // 1. 初始化网络状态服务
      debugPrint('AppInitializer: ${LocalizationService.t('debug_init_network_status')}');
      await NetworkStatusService().initialize();

      // 2. 初始化网络错误处理器
      debugPrint('AppInitializer: ${LocalizationService.t('debug_init_error_handler')}');
      NetworkErrorHandler().initialize();

      // 3. 初始化HTTP客户端
      debugPrint('AppInitializer: ${LocalizationService.t('debug_init_http_client')}');
      await HttpClient().initialize();

      // 4. 初始化调试日志服务
      debugPrint('AppInitializer: 初始化调试日志服务');
      final debugLogService = DebugLogService();
      await debugLogService.initialize();
      if (!debugLogService.isEnabled) {
        await debugLogService.setEnabled(true);
        debugPrint('AppInitializer: 已启用调试日志服务');
      }

      // 5. 初始化API服务
      debugPrint('AppInitializer: ${LocalizationService.t('debug_init_api_service')}');
      await ApiService.initialize();

      // 6. 清理过期缓存
      debugPrint('AppInitializer: ${LocalizationService.t('debug_cleanup_cache')}');
      await CacheService.clearExpiredCache();

      _isInitialized = true;
      debugPrint('AppInitializer: ${LocalizationService.t('debug_all_services_init')}');
      
    } catch (e) {
      debugPrint('AppInitializer: ${LocalizationService.t('debug_init_failed')} - $e');
      rethrow;
    }
  }
  
  /// 检查是否已初始化
  static bool get isInitialized => _isInitialized;
  
  /// 获取网络状态服务
  static NetworkStatusService get networkStatus => NetworkStatusService();
  
  /// 获取网络错误处理器
  static NetworkErrorHandler get errorHandler => NetworkErrorHandler();
  
  /// 获取HTTP客户端
  static HttpClient get httpClient => HttpClient();
  
  /// 销毁所有服务
  static void dispose() {
    debugPrint('AppInitializer: ${LocalizationService.t('debug_dispose_services')}');

    try {
      NetworkStatusService().dispose();
      NetworkErrorHandler().dispose();
      HttpClient().dispose();

      _isInitialized = false;
      debugPrint('AppInitializer: ${LocalizationService.t('debug_all_services_disposed')}');
    } catch (e) {
      debugPrint('AppInitializer: ${LocalizationService.t('debug_dispose_error')} - $e');
    }
  }
}
