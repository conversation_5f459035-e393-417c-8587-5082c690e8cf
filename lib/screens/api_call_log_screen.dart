import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';
import '../services/debug_log_service.dart';

class ApiCallLogScreen extends StatefulWidget {
  const ApiCallLogScreen({super.key});

  @override
  State<ApiCallLogScreen> createState() => _ApiCallLogScreenState();
}

class _ApiCallLogScreenState extends State<ApiCallLogScreen> {
  final DebugLogService _debugLogService = DebugLogService();
  List<LogEntry> _logs = [];
  bool _isLoading = true;
  String? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  Future<void> _loadLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _debugLogService.initialize();

      // 确保日志服务已启用
      if (!_debugLogService.isEnabled) {
        await _debugLogService.setEnabled(true);
        debugPrint('API调用日志：已启用DebugLogService');
      }

      // 只获取API请求和响应日志，并按时间倒序排列（最新的在前面）
      final allLogs = _debugLogService.logs;
      _logs = allLogs.where((log) =>
        log.tag == 'API_REQUEST' ||
        log.tag == 'API_RESPONSE'
      ).toList();

      // 按时间倒序排列
      _logs.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      debugPrint('API调用日志：加载了 ${_logs.length} 条日志');
    } catch (e) {
      debugPrint('加载API日志失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _createTestLogs() async {
    // 创建一些测试API日志
    _debugLogService.logApiRequest(
      'GET',
      'http://************:8080/api/apps/list',
      {'Authorization': 'Bearer bee4adc47bf38c', 'Content-Type': 'application/json'},
      null,
    );

    await Future.delayed(const Duration(milliseconds: 100));

    _debugLogService.logApiResponse(
      'http://************:8080/api/apps/list',
      200,
      {
        'success': true,
        'message': '获取应用列表成功',
        'data': {
          'categories': [
            {
              'name': '考勤打卡',
              'icon': 'access_time',
              'color': '0xFF366CC',
              'apps': [
                {'name': '打卡', 'icon': 'access_time', 'color': '0xFF366CC'}
              ]
            },
            {
              'name': '日程管理',
              'icon': 'event_busy',
              'color': '0xFF4CAF50',
              'apps': [
                {'name': '请假申请', 'icon': 'receipt', 'color': '0xFF4336'}
              ]
            }
          ]
        }
      },
    );

    await Future.delayed(const Duration(milliseconds: 200));

    _debugLogService.logApiRequest(
      'POST',
      'http://************:8080/api/auth/login',
      {'Content-Type': 'application/json'},
      {'username': 'admin', 'password': '123456'},
    );

    await Future.delayed(const Duration(milliseconds: 100));

    _debugLogService.logApiResponse(
      'http://************:8080/api/auth/login',
      200,
      {
        'success': true,
        'message': '登录成功',
        'data': {
          'token': 'bee4adc47bf38c0b29292bf3dda322204bcad283ae0e852e860',
          'user': {
            'id': 1,
            'username': 'admin',
            'name': '管理员Andy',
            'avatar': '',
            'department': 'IT信息技术部'
          }
        }
      },
    );

    // 刷新日志列表
    await _loadLogs();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('test_logs_created')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  Future<void> _clearLogs() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('clear_api_logs')),
        content: Text(LocalizationService.t('confirm_clear_logs')),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(LocalizationService.t('cancel')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(LocalizationService.t('confirm')),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _debugLogService.clearLogs();
      await _loadLogs();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(LocalizationService.t('api_logs_cleared')),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  Future<void> _copyLog(LogEntry log) async {
    await Clipboard.setData(ClipboardData(text: log.formattedMessage));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('api_log_copied')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  Future<void> _copyAllLogs() async {
    final filteredLogs = _getFilteredLogs();
    final logsText = filteredLogs.map((log) => log.formattedMessage).join('\n\n');
    await Clipboard.setData(ClipboardData(text: logsText));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocalizationService.t('api_log_copied')),
          backgroundColor: AppTheme.successColor,
        ),
      );
    }
  }

  void _showLogDetails(LogEntry log) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('view_api_log')),
        content: SingleChildScrollView(
          child: SelectableText(
            log.formattedMessage,
            style: const TextStyle(fontFamily: 'monospace'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => _copyLog(log),
            child: Text(LocalizationService.t('copy')),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocalizationService.t('close')),
          ),
        ],
      ),
    );
  }

  List<LogEntry> _getFilteredLogs() {
    if (_selectedFilter == null) {
      return _logs;
    }
    return _logs.where((log) => log.tag == _selectedFilter).toList();
  }

  Map<String, int> _getLogStats() {
    final stats = <String, int>{};
    for (final log in _logs) {
      final tag = log.tag ?? 'OTHER';
      stats[tag] = (stats[tag] ?? 0) + 1;
    }
    return stats;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.getBackgroundColor(context),
      appBar: AppBar(
        title: Text(
          LocalizationService.t('api_call_log_title'),
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLogs,
            tooltip: LocalizationService.t('refresh'),
          ),
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: _copyAllLogs,
            tooltip: LocalizationService.t('copy_all'),
          ),
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: _createTestLogs,
            tooltip: LocalizationService.t('create_test_logs'),
          ),
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearLogs,
            tooltip: LocalizationService.t('clear_api_logs'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _logs.isEmpty
              ? _buildEmptyState()
              : Column(
                  children: [
                    _buildStatsCard(),
                    if (_selectedFilter != null) _buildFilterHeader(),
                    Expanded(child: _buildLogsList()),
                  ],
                ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.api_outlined,
            size: 64,
            color: AppTheme.getTextSecondaryColor(context),
          ),
          const SizedBox(height: 16),
          Text(
            LocalizationService.t('no_api_logs'),
            style: TextStyle(
              fontSize: 18,
              color: AppTheme.getTextSecondaryColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard() {
    final stats = _getLogStats();
    if (stats.isEmpty) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              LocalizationService.t('log_statistics'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.getTextPrimaryColor(context),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: stats.entries.map((entry) {
                final isSelected = _selectedFilter == entry.key;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedFilter = isSelected ? null : entry.key;
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor : AppTheme.getCardColor(context),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor : AppTheme.getBorderColor(context),
                      ),
                    ),
                    child: Text(
                      '${entry.key} (${entry.value})',
                      style: TextStyle(
                        color: isSelected ? Colors.white : AppTheme.getTextPrimaryColor(context),
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: AppTheme.primaryColor.withValues(alpha: 0.1),
      child: Row(
        children: [
          Text(
            '${LocalizationService.t('filter_by')}: $_selectedFilter',
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          TextButton(
            onPressed: () {
              setState(() {
                _selectedFilter = null;
              });
            },
            child: Text(LocalizationService.t('show_all')),
          ),
        ],
      ),
    );
  }

  Widget _buildLogsList() {
    final filteredLogs = _getFilteredLogs();
    
    if (filteredLogs.isEmpty) {
      return Center(
        child: Text(
          '${LocalizationService.t('no_logs_found')} $_selectedFilter',
          style: TextStyle(
            color: AppTheme.getTextSecondaryColor(context),
            fontSize: 16,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: filteredLogs.length,
      itemBuilder: (context, index) {
        final log = filteredLogs[index];
        return _buildLogItem(log);
      },
    );
  }

  Widget _buildLogItem(LogEntry log) {
    return _ApiLogCard(
      log: log,
      onCopy: () => _copyLog(log),
      onViewDetails: () => _showLogDetails(log),
    );
  }
}

class _ApiLogCard extends StatefulWidget {
  final LogEntry log;
  final VoidCallback onCopy;
  final VoidCallback onViewDetails;

  const _ApiLogCard({
    required this.log,
    required this.onCopy,
    required this.onViewDetails,
  });

  @override
  State<_ApiLogCard> createState() => _ApiLogCardState();
}

class _ApiLogCardState extends State<_ApiLogCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final log = widget.log;
    final isRequest = log.tag == 'API_REQUEST';
    final isResponse = log.tag == 'API_RESPONSE';

    Color cardColor = AppTheme.getCardColor(context);
    Color borderColor = AppTheme.getBorderColor(context);
    IconData icon = Icons.info_outline;
    String typeLabel = log.tag ?? 'API';

    if (isRequest) {
      cardColor = Colors.blue.withValues(alpha: 0.1);
      borderColor = Colors.blue;
      icon = Icons.arrow_upward;
      typeLabel = LocalizationService.t('api_request');
    } else if (isResponse) {
      cardColor = Colors.green.withValues(alpha: 0.1);
      borderColor = Colors.green;
      icon = Icons.arrow_downward;
      typeLabel = LocalizationService.t('api_response');
    }

    // 解析API信息
    final apiInfo = _parseApiInfo(log.message);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      color: cardColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: borderColor, width: 1),
      ),
      child: Column(
        children: [
          ListTile(
            leading: Icon(icon, color: borderColor),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    typeLabel,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: borderColor,
                    ),
                  ),
                ),
                Text(
                  '${log.timestamp.hour.toString().padLeft(2, '0')}:'
                  '${log.timestamp.minute.toString().padLeft(2, '0')}:'
                  '${log.timestamp.second.toString().padLeft(2, '0')}',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.getTextSecondaryColor(context),
                  ),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (apiInfo['method'] != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: borderColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          apiInfo['method']!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          apiInfo['url'] ?? '',
                          style: TextStyle(
                            fontSize: 12,
                            color: AppTheme.getTextPrimaryColor(context),
                            fontFamily: 'monospace',
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
                if (apiInfo['status'] != null) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Text(
                        '${LocalizationService.t('status_code')}: ',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.getTextSecondaryColor(context),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getStatusColor(apiInfo['status']!),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          apiInfo['status']!,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                if (!_isExpanded && apiInfo['summary'] != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    apiInfo['summary']!,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppTheme.getTextSecondaryColor(context),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    size: 20,
                  ),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  tooltip: _isExpanded ? LocalizationService.t('collapse') : LocalizationService.t('expand'),
                ),
                IconButton(
                  icon: const Icon(Icons.copy, size: 20),
                  onPressed: widget.onCopy,
                  tooltip: LocalizationService.t('copy_api_log'),
                ),
                IconButton(
                  icon: const Icon(Icons.visibility, size: 20),
                  onPressed: widget.onViewDetails,
                  tooltip: LocalizationService.t('view_api_log'),
                ),
              ],
            ),
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
          ),
          if (_isExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocalizationService.t('detailed_info'),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: borderColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.getBackgroundColor(context),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppTheme.getBorderColor(context),
                      ),
                    ),
                    child: SelectableText(
                      _formatLogMessage(log.message),
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                        color: AppTheme.getTextPrimaryColor(context),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Map<String, String> _parseApiInfo(String message) {
    final info = <String, String>{};

    // 解析请求方法和URL
    final methodMatch = RegExp(r'方法:\s*(\w+)').firstMatch(message);
    if (methodMatch != null) {
      info['method'] = methodMatch.group(1)!;
    }

    final urlMatch = RegExp(r'URL:\s*([^\n]+)').firstMatch(message);
    if (urlMatch != null) {
      info['url'] = urlMatch.group(1)!.trim();
    }

    // 解析状态码
    final statusMatch = RegExp(r'状态码:\s*(\d+)').firstMatch(message);
    if (statusMatch != null) {
      info['status'] = statusMatch.group(1)!;
    }

    // 生成摘要
    if (message.contains('登录')) {
      if (message.contains('成功')) {
        info['summary'] = '登录成功';
      } else if (message.contains('失败')) {
        info['summary'] = '登录失败';
      } else {
        info['summary'] = '登录接口调用';
      }
    } else if (message.contains('API')) {
      if (message.contains('调用失败')) {
        info['summary'] = 'API调用失败';
      } else if (message.contains('成功')) {
        info['summary'] = 'API调用成功';
      } else {
        info['summary'] = 'API相关操作';
      }
    }

    return info;
  }

  String _formatLogMessage(String message) {
    try {
      // 尝试提取和格式化JSON数据
      final lines = message.split('\n');
      final formattedLines = <String>[];

      for (final line in lines) {
        if (line.contains('响应数据:') || line.contains('请求体:')) {
          final parts = line.split(':');
          if (parts.length >= 2) {
            final jsonPart = parts.sublist(1).join(':').trim();
            try {
              final jsonObj = jsonDecode(jsonPart);
              final prettyJson = const JsonEncoder.withIndent('  ').convert(jsonObj);
              formattedLines.add('${parts[0]}:');
              formattedLines.add(prettyJson);
            } catch (e) {
              formattedLines.add(line);
            }
          } else {
            formattedLines.add(line);
          }
        } else {
          formattedLines.add(line);
        }
      }

      return formattedLines.join('\n');
    } catch (e) {
      return message;
    }
  }

  Color _getStatusColor(String status) {
    final code = int.tryParse(status) ?? 0;
    if (code >= 200 && code < 300) {
      return Colors.green;
    } else if (code >= 400 && code < 500) {
      return Colors.orange;
    } else if (code >= 500) {
      return Colors.red;
    } else {
      return Colors.grey;
    }
  }
}
