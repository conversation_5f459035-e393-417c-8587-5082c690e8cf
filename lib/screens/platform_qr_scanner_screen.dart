import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../constants/app_theme.dart';
import '../services/localization_service.dart';

// 条件导入
import 'web_qr_scanner_screen.dart' if (dart.library.io) 'qr_scanner_screen.dart';

class PlatformQRScannerScreen extends StatelessWidget {
  const PlatformQRScannerScreen({super.key});

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // Web平台使用Web版本的扫描器
      return const WebQRScannerScreen();
    } else {
      // 移动平台使用原生扫描器
      return const QRScannerFallbackScreen();
    }
  }
}

// 移动平台的回退实现（当mobile_scanner不可用时）
class QRScannerFallbackScreen extends StatefulWidget {
  const QRScannerFallbackScreen({super.key});

  @override
  State<QRScannerFallbackScreen> createState() => _QRScannerFallbackScreenState();
}

class _QRScannerFallbackScreenState extends State<QRScannerFallbackScreen> {
  String? result;

  void _showManualInputDialog() {
    final controller = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocalizationService.t('manual_input')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(LocalizationService.t('qr_input_hint')),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: LocalizationService.t('qr_example'),
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(LocalizationService.t('cancel')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (controller.text.isNotEmpty) {
                Navigator.of(context).pop(controller.text);
              }
            },
            child: Text(LocalizationService.t('confirm')),
          ),
        ],
      ),
    );
  }

  void _simulateQRCodeDetection(String qrData) {
    setState(() {
      result = qrData;
    });
  }

  void _confirmResult() {
    Navigator.of(context).pop(result);
  }

  void _continueScan() {
    setState(() {
      result = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(LocalizationService.t('scan_qr_code')),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showManualInputDialog,
            icon: const Icon(Icons.keyboard),
            tooltip: LocalizationService.t('manual_input'),
          ),
        ],
      ),
      body: result != null ? _buildResultView() : _buildScannerView(),
    );
  }

  Widget _buildScannerView() {
    return Stack(
      children: [
        // 模拟相机预览区域
        Positioned.fill(
          child: Container(
            color: Colors.black,
            child: const Center(
              child: Text(
                '相机预览区域\n(移动平台需要mobile_scanner插件)',
                style: TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
        // 扫描框
        Positioned.fill(
          child: CustomPaint(
            painter: QRScannerOverlayPainter(),
          ),
        ),
        // 底部提示
        Positioned(
          bottom: 100,
          left: 0,
          right: 0,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              LocalizationService.t('scan_instruction'),
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        // 测试按钮（仅用于演示）
        Positioned(
          bottom: 20,
          left: 0,
          right: 0,
          child: Center(
            child: ElevatedButton(
              onPressed: () => _simulateQRCodeDetection('192.168.1.100:8080,测试服务器'),
              child: const Text('模拟扫描成功'),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultView() {
    return Container(
      color: Colors.black,
      child: Column(
        children: [
          Expanded(
            child: Center(
              child: Container(
                margin: const EdgeInsets.all(20),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: AppTheme.successColor,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      LocalizationService.t('scan_success'),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        result!,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _continueScan,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.white,
                      side: const BorderSide(color: Colors.white),
                    ),
                    child: Text(LocalizationService.t('continue_scan')),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _confirmResult,
                    child: Text(LocalizationService.t('use_result')),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class QRScannerOverlayPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.fill;

    final scanAreaSize = size.width * 0.7;
    final scanAreaLeft = (size.width - scanAreaSize) / 2;
    final scanAreaTop = (size.height - scanAreaSize) / 2;

    // 绘制遮罩
    canvas.drawPath(
      Path()
        ..addRect(Rect.fromLTWH(0, 0, size.width, size.height))
        ..addRect(Rect.fromLTWH(scanAreaLeft, scanAreaTop, scanAreaSize, scanAreaSize))
        ..fillType = PathFillType.evenOdd,
      paint,
    );

    // 绘制扫描框边角
    final cornerPaint = Paint()
      ..color = AppTheme.primaryColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4;

    final cornerLength = 20.0;

    // 左上角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft, scanAreaTop + cornerLength)
        ..lineTo(scanAreaLeft, scanAreaTop)
        ..lineTo(scanAreaLeft + cornerLength, scanAreaTop),
      cornerPaint,
    );

    // 右上角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft + scanAreaSize - cornerLength, scanAreaTop)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop + cornerLength),
      cornerPaint,
    );

    // 左下角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft, scanAreaTop + scanAreaSize - cornerLength)
        ..lineTo(scanAreaLeft, scanAreaTop + scanAreaSize)
        ..lineTo(scanAreaLeft + cornerLength, scanAreaTop + scanAreaSize),
      cornerPaint,
    );

    // 右下角
    canvas.drawPath(
      Path()
        ..moveTo(scanAreaLeft + scanAreaSize - cornerLength, scanAreaTop + scanAreaSize)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop + scanAreaSize)
        ..lineTo(scanAreaLeft + scanAreaSize, scanAreaTop + scanAreaSize - cornerLength),
      cornerPaint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
